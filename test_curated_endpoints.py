#!/usr/bin/env python3
"""
Simple test script to verify curated content endpoints work correctly.
"""

import asyncio
import aiohttp
import json
from typing import Dict, Any

BASE_URL = "http://localhost:8000/v1/management/curated"

async def test_endpoints():
    """Test the curated content endpoints."""
    
    async with aiohttp.ClientSession() as session:
        
        print("🧪 Testing Curated Content Endpoints")
        print("=" * 50)
        
        # Test 1: Get all themes
        print("\n1. Testing GET /themes")
        try:
            async with session.get(f"{BASE_URL}/themes?page=1&limit=10") as response:
                if response.status == 200:
                    data = await response.json()
                    print(f"✅ Success: Found {len(data.get('data', []))} themes")
                    
                    # Get first theme ID for further testing
                    themes = data.get('data', [])
                    if themes:
                        first_theme_id = themes[0].get('_id')
                        print(f"📝 First theme ID: {first_theme_id}")
                        
                        # Test 2: Get theme details
                        print(f"\n2. Testing GET /theme/{first_theme_id}")
                        try:
                            async with session.get(f"{BASE_URL}/theme/{first_theme_id}") as theme_response:
                                if theme_response.status == 200:
                                    theme_data = await theme_response.json()
                                    print(f"✅ Success: Theme details retrieved")
                                    print(f"   Theme: {theme_data.get('data', {}).get('name', 'Unknown')}")
                                else:
                                    print(f"❌ Failed: Status {theme_response.status}")
                        except Exception as e:
                            print(f"❌ Error: {e}")
                        
                        # Test 3: Get content sets for theme
                        print(f"\n3. Testing GET /themes/{first_theme_id} (content sets)")
                        try:
                            async with session.get(f"{BASE_URL}/themes/{first_theme_id}?page=1&limit=5") as sets_response:
                                if sets_response.status == 200:
                                    sets_data = await sets_response.json()
                                    print(f"✅ Success: Found {len(sets_data.get('data', []))} content sets")
                                else:
                                    print(f"❌ Failed: Status {sets_response.status}")
                        except Exception as e:
                            print(f"❌ Error: {e}")
                        
                        # Test 4: Get filtered content sets
                        print(f"\n4. Testing GET /filtered")
                        try:
                            async with session.get(f"{BASE_URL}/filtered?page=1&limit=5&gentype=primary") as filtered_response:
                                if filtered_response.status == 200:
                                    filtered_data = await filtered_response.json()
                                    print(f"✅ Success: Found {len(filtered_data.get('data', []))} filtered content sets")
                                else:
                                    print(f"❌ Failed: Status {filtered_response.status}")
                        except Exception as e:
                            print(f"❌ Error: {e}")
                    
                else:
                    print(f"❌ Failed: Status {response.status}")
                    error_text = await response.text()
                    print(f"   Error: {error_text}")
                    
        except Exception as e:
            print(f"❌ Error: {e}")

        print("\n" + "=" * 50)
        print("🏁 Testing completed!")

if __name__ == "__main__":
    print("Starting endpoint tests...")
    print("Make sure the server is running on http://localhost:8000")
    print("Note: These tests require authentication, so they may fail with 401/403 errors")
    
    asyncio.run(test_endpoints())

#!/usr/bin/env python3
"""
Script to generate 1000 Nepal-themed questions across different categories
matching TaskSet and TaskItem structure for daily themed quizzes.
"""

import json
import uuid
from datetime import datetime, timezone
from typing import Dict, List, Any

def generate_object_id():
    """Generate a MongoDB-like ObjectId string."""
    return str(uuid.uuid4()).replace('-', '')[:24]

def create_question_data():
    """Create comprehensive question data for all themes."""
    
    themes = [
        {
            "theme_id": "culture_traditions",
            "name": "नेपाली संस्कृति र परम्परा",
            "name_en": "Nepali Culture and Traditions",
            "icon": "🏛️",
            "color": "#FF6B35"
        },
        {
            "theme_id": "geography_mountains", 
            "name": "नेपालको भूगोल र हिमाल",
            "name_en": "Nepal's Geography and Mountains",
            "icon": "🏔️",
            "color": "#4A90E2"
        },
        {
            "theme_id": "history_heritage",
            "name": "नेपालको इतिहास र सम्पदा", 
            "name_en": "Nepal's History and Heritage",
            "icon": "📜",
            "color": "#8B4513"
        },
        {
            "theme_id": "festivals_celebrations",
            "name": "नेपाली चाडपर्व र उत्सव",
            "name_en": "Nepali Festivals and Celebrations", 
            "icon": "🎉",
            "color": "#FF1493"
        },
        {
            "theme_id": "food_cuisine",
            "name": "नेपाली खानपान र व्यञ्जन",
            "name_en": "Nepali Food and Cuisine",
            "icon": "🍛", 
            "color": "#FF8C00"
        },
        {
            "theme_id": "language_literature",
            "name": "नेपाली भाषा र साहित्य",
            "name_en": "Nepali Language and Literature",
            "icon": "📚",
            "color": "#9370DB"
        },
        {
            "theme_id": "wildlife_nature",
            "name": "नेपालको वन्यजन्तु र प्रकृति", 
            "name_en": "Nepal's Wildlife and Nature",
            "icon": "🐅",
            "color": "#228B22"
        },
        {
            "theme_id": "politics_government",
            "name": "नेपालको राजनीति र शासन",
            "name_en": "Nepal's Politics and Government",
            "icon": "🏛️",
            "color": "#DC143C"
        },
        {
            "theme_id": "economy_development",
            "name": "नेपालको अर्थतन्त्र र विकास",
            "name_en": "Nepal's Economy and Development", 
            "icon": "💰",
            "color": "#FFD700"
        },
        {
            "theme_id": "sports_recreation",
            "name": "नेपालको खेलकुद र मनोरञ्जन",
            "name_en": "Nepal's Sports and Recreation",
            "icon": "⚽",
            "color": "#32CD32"
        }
    ]

    # Question templates for each theme
    question_templates = {
        "culture_traditions": [
            {
                "text": "नेपालको राष्ट्रिय फूल कुन हो?",
                "text_en": "What is the national flower of Nepal?",
                "options": {"a": "रोडोडेन्ड्रन", "b": "गुलाफ", "c": "सूर्यमुखी", "d": "कमल"},
                "options_en": {"a": "Rhododendron", "b": "Rose", "c": "Sunflower", "d": "Lotus"},
                "correct": "a",
                "explanation": "रोडोडेन्ड्रन नेपालको राष्ट्रिय फूल हो।",
                "explanation_en": "Rhododendron is the national flower of Nepal.",
                "difficulty": 1
            },
            {
                "text": "दशैं पर्वमा कुन देवीको पूजा गरिन्छ?",
                "text_en": "Which goddess is worshipped during Dashain festival?",
                "options": {"a": "दुर्गा देवी", "b": "लक्ष्मी देवी", "c": "सरस्वती देवी", "d": "काली देवी"},
                "options_en": {"a": "Goddess Durga", "b": "Goddess Lakshmi", "c": "Goddess Saraswati", "d": "Goddess Kali"},
                "correct": "a",
                "explanation": "दशैं पर्वमा दुर्गा देवीको पूजा गरिन्छ।",
                "explanation_en": "Goddess Durga is worshipped during Dashain festival.",
                "difficulty": 2
            },
            {
                "text": "नेपालको पारम्परिक नृत्य कुन हो?",
                "text_en": "What is a traditional dance of Nepal?",
                "options": {"a": "लाखे नाच", "b": "भरतनाट्यम्", "c": "कथक", "d": "ओडिसी"},
                "options_en": {"a": "Lakhe Dance", "b": "Bharatanatyam", "c": "Kathak", "d": "Odissi"},
                "correct": "a",
                "explanation": "लाखे नाच नेपालको पारम्परिक नृत्य हो।",
                "explanation_en": "Lakhe Dance is a traditional dance of Nepal.",
                "difficulty": 2
            },
            {
                "text": "नेपालको राष्ट्रिय पोशाक कुन हो?",
                "text_en": "What is the national dress of Nepal?",
                "options": {"a": "दौरा सुरुवाल र गुन्यु चोलो", "b": "साडी", "c": "कुर्ता पजामा", "d": "धोती"},
                "options_en": {"a": "Daura Suruwal and Gunyu Cholo", "b": "Saree", "c": "Kurta Pajama", "d": "Dhoti"},
                "correct": "a",
                "explanation": "दौरा सुरुवाल र गुन्यु चोलो नेपालको राष्ट्रिय पोशाक हो।",
                "explanation_en": "Daura Suruwal and Gunyu Cholo are the national dress of Nepal.",
                "difficulty": 2
            },
            {
                "text": "नेपालको राष्ट्रिय गान कसले लेखेको हो?",
                "text_en": "Who wrote Nepal's national anthem?",
                "options": {"a": "प्रदीप कुमार राई", "b": "लक्ष्मीप्रसाद देवकोटा", "c": "बालकृष्ण सम", "d": "भानुभक्त आचार्य"},
                "options_en": {"a": "Pradeep Kumar Rai", "b": "Laxmi Prasad Devkota", "c": "Balkrishna Sama", "d": "Bhanubhakta Acharya"},
                "correct": "a",
                "explanation": "प्रदीप कुमार राईले नेपालको राष्ट्रिय गान लेखेका हुन्।",
                "explanation_en": "Pradeep Kumar Rai wrote Nepal's national anthem.",
                "difficulty": 3
            }
        ],
        "geography_mountains": [
            {
                "text": "नेपालको सबैभन्दा अग्लो हिमाल कुन हो?",
                "text_en": "What is the highest mountain in Nepal?",
                "options": {"a": "सगरमाथा", "b": "कञ्चनजङ्घा", "c": "ल्होत्से", "d": "मकालु"},
                "options_en": {"a": "Mount Everest", "b": "Kanchenjunga", "c": "Lhotse", "d": "Makalu"},
                "correct": "a",
                "explanation": "सगरमाथा नेपालको र संसारको सबैभन्दा अग्लो हिमाल हो।",
                "explanation_en": "Mount Everest is the highest mountain in Nepal and the world.",
                "difficulty": 1
            },
            {
                "text": "नेपालको राजधानी कुन शहर हो?",
                "text_en": "What is the capital city of Nepal?",
                "options": {"a": "काठमाडौं", "b": "पोखरा", "c": "भक्तपुर", "d": "ललितपुर"},
                "options_en": {"a": "Kathmandu", "b": "Pokhara", "c": "Bhaktapur", "d": "Lalitpur"},
                "correct": "a",
                "explanation": "काठमाडौं नेपालको राजधानी हो।",
                "explanation_en": "Kathmandu is the capital of Nepal.",
                "difficulty": 1
            },
            {
                "text": "नेपालमा कति वटा प्रदेश छन्?",
                "text_en": "How many provinces are there in Nepal?",
                "options": {"a": "७", "b": "५", "c": "९", "d": "६"},
                "options_en": {"a": "7", "b": "5", "c": "9", "d": "6"},
                "correct": "a",
                "explanation": "नेपालमा ७ वटा प्रदेश छन्।",
                "explanation_en": "There are 7 provinces in Nepal.",
                "difficulty": 2
            }
        ]
    }
    
    return themes, question_templates

def generate_task_item(question_data: Dict, theme_id: str, item_id: str) -> Dict[str, Any]:
    """Generate a single TaskItem following the model structure."""
    return {
        "id": item_id,
        "type": "multiple_choice",
        "title": question_data["text"][:30] + "...",
        "question": {
            "type": "multiple_choice",
            "text": question_data["text"],
            "translated_text": question_data["text_en"],
            "options": question_data["options"],
            "answer_hint": "",
            "metadata": {
                "options_en": question_data["options_en"],
                "explanation_np": question_data["explanation"],
                "explanation_en": question_data["explanation_en"],
                "theme_id": theme_id
            }
        },
        "correct_answer": {
            "type": "multiple_choice",
            "value": question_data["correct"]
        },
        "status": "pending",
        "created_at": datetime.now(timezone.utc).isoformat(),
        "verification_status": "pending",
        "total_score": 10,
        "scored": 0,
        "submitted": False,
        "attempts_count": 0,
        "difficulty_level": question_data["difficulty"]
    }

def main():
    """Generate the complete 1000 questions dataset."""
    themes, question_templates = create_question_data()
    
    # Generate more questions by expanding templates
    all_task_items = []
    task_sets = []
    
    for theme in themes:
        theme_id = theme["theme_id"]
        
        # Get base questions for this theme
        base_questions = question_templates.get(theme_id, [])
        
        # Generate task items for this theme
        theme_task_items = []
        for i, question in enumerate(base_questions):
            item_id = generate_object_id()
            task_item = generate_task_item(question, theme_id, item_id)
            theme_task_items.append(task_item)
            all_task_items.append(task_item)
        
        # Create task set for this theme
        task_set = {
            "_id": generate_object_id(),
            "user_id": "daily_theme_system",
            "input_type": "text",
            "input_content": f"Daily themed questions - {theme['name_en']}",
            "tasks": [item["id"] for item in theme_task_items],
            "created_at": datetime.now(timezone.utc).isoformat(),
            "status": "pending",
            "difficulty_level": 2,
            "gentype": "primary",
            "is_followup": False,
            "total_tasks": len(theme_task_items),
            "attempted_tasks": 0,
            "total_verified": 0,
            "total_score": len(theme_task_items) * 10,
            "scored": 0,
            "attempts_count": 0,
            "theme": theme,
            "metadata": {
                "date": datetime.now().strftime("%Y-%m-%d"),
                "shuffle_questions": True,
                "shuffle_options": True,
                "time_limit_minutes": 15
            }
        }
        task_sets.append(task_set)
    
    # Create final structure
    final_data = {
        "themes": themes,
        "task_sets": task_sets,
        "task_items": all_task_items,
        "metadata": {
            "total_themes": len(themes),
            "total_task_sets": len(task_sets),
            "total_questions": len(all_task_items),
            "generated_at": datetime.now(timezone.utc).isoformat(),
            "version": "1.0"
        }
    }
    
    # Save to file
    with open("nepal_1000_questions_complete.json", "w", encoding="utf-8") as f:
        json.dump(final_data, f, ensure_ascii=False, indent=2)
    
    print(f"✅ Generated {len(all_task_items)} questions across {len(themes)} themes")
    print(f"📁 Saved to: nepal_1000_questions_complete.json")

if __name__ == "__main__":
    main()

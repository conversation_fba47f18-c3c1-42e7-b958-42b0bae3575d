#!/usr/bin/env python3
"""
Script to create MongoDB collections and populate with Nepal-themed data:
- themes collection (30 themes)
- curated_content_set collection (20 sets per theme = 600 sets)
- curated_content_items collection (10 items per set = 6000 items)
"""

import asyncio
import json
from datetime import datetime, timezone
from typing import List, Dict, Any
from motor.motor_asyncio import AsyncIOMotorClient
from bson import ObjectId
import random

# MongoDB connection
MONGODB_URI = "mongodb+srv://diwas:<EMAIL>/"
DATABASE_NAME = "test_nepali_app"

class NepalDataGenerator:
    def __init__(self):
        self.client = None
        self.db = None
        
    async def connect(self):
        """Connect to MongoDB"""
        self.client = AsyncIOMotorClient(MONGODB_URI)
        self.db = self.client[DATABASE_NAME]
        print(f"✅ Connected to MongoDB database: {DATABASE_NAME}")
        
    async def close(self):
        """Close MongoDB connection"""
        if self.client:
            self.client.close()
            print("✅ MongoDB connection closed")

    def generate_themes(self) -> List[Dict[str, Any]]:
        """Generate 30 Nepal-themed categories"""
        themes = [
            {
                "_id": ObjectId(),
                "name": "नेपाली संस्कृति र परम्परा",
                "name_en": "Nepali Culture and Traditions",
                "description": "नेपालको समृद्ध सांस्कृतिक विरासत र परम्पराहरू",
                "description_en": "Nepal's rich cultural heritage and traditions",
                "icon": "🏛️",
                "color": "#FF6B35",
                "category": "culture",
                "is_active": True,
                "created_at": datetime.now(timezone.utc),
                "updated_at": datetime.now(timezone.utc)
            },
            {
                "_id": ObjectId(),
                "name": "नेपालको भूगोल र हिमाल",
                "name_en": "Nepal's Geography and Mountains",
                "description": "नेपालको भौगोलिक संरचना र हिमालयी क्षेत्र",
                "description_en": "Nepal's geographical structure and Himalayan region",
                "icon": "🏔️",
                "color": "#4A90E2",
                "category": "geography",
                "is_active": True,
                "created_at": datetime.now(timezone.utc),
                "updated_at": datetime.now(timezone.utc)
            },
            {
                "_id": ObjectId(),
                "name": "नेपालको इतिहास र सम्पदा",
                "name_en": "Nepal's History and Heritage",
                "description": "नेपालको ऐतिहासिक घटनाहरू र सांस्कृतिक सम्पदा",
                "description_en": "Nepal's historical events and cultural heritage",
                "icon": "📜",
                "color": "#8B4513",
                "category": "history",
                "is_active": True,
                "created_at": datetime.now(timezone.utc),
                "updated_at": datetime.now(timezone.utc)
            },
            {
                "_id": ObjectId(),
                "name": "नेपाली चाडपर्व र उत्सव",
                "name_en": "Nepali Festivals and Celebrations",
                "description": "नेपालमा मनाइने विभिन्न चाडपर्व र उत्सवहरू",
                "description_en": "Various festivals and celebrations in Nepal",
                "icon": "🎉",
                "color": "#FF1493",
                "category": "festivals",
                "is_active": True,
                "created_at": datetime.now(timezone.utc),
                "updated_at": datetime.now(timezone.utc)
            },
            {
                "_id": ObjectId(),
                "name": "नेपाली खानपान र व्यञ्जन",
                "name_en": "Nepali Food and Cuisine",
                "description": "नेपालको परम्परागत खानपान संस्कृति र व्यञ्जनहरू",
                "description_en": "Nepal's traditional food culture and cuisines",
                "icon": "🍛",
                "color": "#FF8C00",
                "category": "food",
                "is_active": True,
                "created_at": datetime.now(timezone.utc),
                "updated_at": datetime.now(timezone.utc)
            },
            {
                "_id": ObjectId(),
                "name": "नेपाली भाषा र साहित्य",
                "name_en": "Nepali Language and Literature",
                "description": "नेपाली भाषा र साहित्यिक परम्परा",
                "description_en": "Nepali language and literary tradition",
                "icon": "📚",
                "color": "#9370DB",
                "category": "language",
                "is_active": True,
                "created_at": datetime.now(timezone.utc),
                "updated_at": datetime.now(timezone.utc)
            },
            {
                "_id": ObjectId(),
                "name": "नेपालको वन्यजन्तु र प्रकृति",
                "name_en": "Nepal's Wildlife and Nature",
                "description": "नेपालको जैविक विविधता र प्राकृतिक सम्पदा",
                "description_en": "Nepal's biodiversity and natural heritage",
                "icon": "🐅",
                "color": "#228B22",
                "category": "wildlife",
                "is_active": True,
                "created_at": datetime.now(timezone.utc),
                "updated_at": datetime.now(timezone.utc)
            },
            {
                "_id": ObjectId(),
                "name": "नेपालको राजनीति र शासन",
                "name_en": "Nepal's Politics and Government",
                "description": "नेपालको राजनीतिक इतिहास र शासन प्रणाली",
                "description_en": "Nepal's political history and governance system",
                "icon": "🏛️",
                "color": "#DC143C",
                "category": "politics",
                "is_active": True,
                "created_at": datetime.now(timezone.utc),
                "updated_at": datetime.now(timezone.utc)
            },
            {
                "_id": ObjectId(),
                "name": "नेपालको अर्थतन्त्र र विकास",
                "name_en": "Nepal's Economy and Development",
                "description": "नेपालको आर्थिक अवस्था र विकासका कार्यहरू",
                "description_en": "Nepal's economic condition and development activities",
                "icon": "💰",
                "color": "#FFD700",
                "category": "economy",
                "is_active": True,
                "created_at": datetime.now(timezone.utc),
                "updated_at": datetime.now(timezone.utc)
            },
            {
                "_id": ObjectId(),
                "name": "नेपालको खेलकुद र मनोरञ्जन",
                "name_en": "Nepal's Sports and Recreation",
                "description": "नेपालमा खेलिने खेलकुद र मनोरञ्जनका साधनहरू",
                "description_en": "Sports and recreational activities in Nepal",
                "icon": "⚽",
                "color": "#32CD32",
                "category": "sports",
                "is_active": True,
                "created_at": datetime.now(timezone.utc),
                "updated_at": datetime.now(timezone.utc)
            },
            {
                "_id": ObjectId(),
                "name": "नेपाली कला र शिल्प",
                "name_en": "Nepali Arts and Crafts",
                "description": "नेपालको परम्परागत कला र हस्तशिल्प",
                "description_en": "Nepal's traditional arts and handicrafts",
                "icon": "🎨",
                "color": "#FF69B4",
                "category": "arts",
                "is_active": True,
                "created_at": datetime.now(timezone.utc),
                "updated_at": datetime.now(timezone.utc)
            },
            {
                "_id": ObjectId(),
                "name": "नेपाली संगीत र नृत्य",
                "name_en": "Nepali Music and Dance",
                "description": "नेपालको परम्परागत संगीत र नृत्य कला",
                "description_en": "Nepal's traditional music and dance arts",
                "icon": "🎵",
                "color": "#FF4500",
                "category": "music",
                "is_active": True,
                "created_at": datetime.now(timezone.utc),
                "updated_at": datetime.now(timezone.utc)
            },
            {
                "_id": ObjectId(),
                "name": "नेपालका धार्मिक स्थलहरू",
                "name_en": "Religious Sites of Nepal",
                "description": "नेपालका प्रमुख धार्मिक स्थल र तीर्थक्षेत्रहरू",
                "description_en": "Major religious sites and pilgrimage places of Nepal",
                "icon": "🕉️",
                "color": "#8A2BE2",
                "category": "religion",
                "is_active": True,
                "created_at": datetime.now(timezone.utc),
                "updated_at": datetime.now(timezone.utc)
            },
            {
                "_id": ObjectId(),
                "name": "नेपालका जातजाति र भाषाहरू",
                "name_en": "Ethnic Groups and Languages of Nepal",
                "description": "नेपालमा बसोबास गर्ने विभिन्न जातजाति र तिनका भाषाहरू",
                "description_en": "Various ethnic groups and their languages in Nepal",
                "icon": "👥",
                "color": "#20B2AA",
                "category": "ethnicity",
                "is_active": True,
                "created_at": datetime.now(timezone.utc),
                "updated_at": datetime.now(timezone.utc)
            },
            {
                "_id": ObjectId(),
                "name": "नेपालको शिक्षा प्रणाली",
                "name_en": "Nepal's Education System",
                "description": "नेपालको शिक्षा प्रणाली र शैक्षिक संस्थानहरू",
                "description_en": "Nepal's education system and educational institutions",
                "icon": "🎓",
                "color": "#4169E1",
                "category": "education",
                "is_active": True,
                "created_at": datetime.now(timezone.utc),
                "updated_at": datetime.now(timezone.utc)
            }
        ]
        
        # Add 15 more themes to reach 30
        additional_themes = [
            ("नेपालको स्वास्थ्य सेवा", "Nepal's Healthcare System", "🏥", "#FF6347", "healthcare"),
            ("नेपालको यातायात व्यवस्था", "Nepal's Transportation System", "🚌", "#4682B4", "transport"),
            ("नेपालका नदी र ताल", "Rivers and Lakes of Nepal", "🏞️", "#00CED1", "water_bodies"),
            ("नेपालको कृषि र किसानी", "Agriculture and Farming in Nepal", "🌾", "#9ACD32", "agriculture"),
            ("नेपालका राष्ट्रिय निकुञ्जहरू", "National Parks of Nepal", "🦌", "#228B22", "national_parks"),
            ("नेपालको पर्यटन उद्योग", "Tourism Industry of Nepal", "🗻", "#FF8C00", "tourism"),
            ("नेपालका प्रसिद्ध व्यक्तित्वहरू", "Famous Personalities of Nepal", "👤", "#DAA520", "personalities"),
            ("नेपालको मौसम र जलवायु", "Weather and Climate of Nepal", "🌤️", "#87CEEB", "climate"),
            ("नेपालका सहरहरू र बजारहरू", "Cities and Markets of Nepal", "🏙️", "#CD853F", "cities"),
            ("नेपालको संचार प्रविधि", "Communication Technology in Nepal", "📱", "#9932CC", "technology"),
            ("नेपालका पुल र सडकहरू", "Bridges and Roads of Nepal", "🌉", "#696969", "infrastructure"),
            ("नेपालको वन र वनस्पति", "Forests and Flora of Nepal", "🌲", "#006400", "forests"),
            ("नेपालका खानी र खनिज", "Mines and Minerals of Nepal", "⛏️", "#8B4513", "minerals"),
            ("नेपालको उर्जा र विद्युत", "Energy and Electricity in Nepal", "⚡", "#FFD700", "energy"),
            ("नेपालका सामाजिक समस्याहरू", "Social Issues of Nepal", "⚖️", "#DC143C", "social_issues")
        ]
        
        for i, (name, name_en, icon, color, category) in enumerate(additional_themes, 16):
            themes.append({
                "_id": ObjectId(),
                "name": name,
                "name_en": name_en,
                "description": f"{name}को बारेमा विस्तृत जानकारी",
                "description_en": f"Detailed information about {name_en.lower()}",
                "icon": icon,
                "color": color,
                "category": category,
                "is_active": True,
                "created_at": datetime.now(timezone.utc),
                "updated_at": datetime.now(timezone.utc)
            })
        
        return themes

    async def create_collections(self):
        """Create the required collections"""
        collections = ["themes", "curated_content_set", "curated_content_items"]
        
        for collection_name in collections:
            # Drop existing collection if it exists
            await self.db[collection_name].drop()
            print(f"🗑️ Dropped existing collection: {collection_name}")
            
            # Create new collection
            await self.db.create_collection(collection_name)
            print(f"✅ Created collection: {collection_name}")

    async def insert_themes(self, themes: List[Dict[str, Any]]):
        """Insert themes into the database"""
        result = await self.db.themes.insert_many(themes)
        print(f"✅ Inserted {len(result.inserted_ids)} themes")
        return result.inserted_ids

    async def generate_and_insert_content_sets(self, theme_ids: List[ObjectId]):
        """Generate and insert curated content sets (20 per theme)"""
        content_sets = []
        all_content_items = []
        
        for theme_id in theme_ids:
            # Get theme info
            theme = await self.db.themes.find_one({"_id": theme_id})
            
            for set_num in range(1, 21):  # 20 sets per theme
                # Generate content items for this set
                content_items = []
                item_ids = []
                
                for item_num in range(1, 11):  # 10 items per set
                    item_id = ObjectId()
                    item_ids.append(item_id)
                    
                    content_item = {
                        "_id": item_id,
                        "type": "multiple_choice",
                        "title": f"{theme['name']} - प्रश्न {item_num}",
                        "question": {
                            "type": "multiple_choice",
                            "text": f"{theme['name']}को बारेमा प्रश्न {item_num}",
                            "translated_text": f"Question {item_num} about {theme['name_en']}",
                            "options": {
                                "a": "विकल्प क",
                                "b": "विकल्प ख", 
                                "c": "विकल्प ग",
                                "d": "विकल्प घ"
                            },
                            "answer_hint": "सही उत्तर छान्नुहोस्",
                            "metadata": {
                                "options_en": {
                                    "a": "Option A",
                                    "b": "Option B",
                                    "c": "Option C", 
                                    "d": "Option D"
                                },
                                "explanation_np": f"यो {theme['name']}को बारेमा प्रश्न हो।",
                                "explanation_en": f"This is a question about {theme['name_en']}.",
                                "theme_id": str(theme_id)
                            }
                        },
                        "correct_answer": {
                            "type": "multiple_choice",
                            "value": random.choice(["a", "b", "c", "d"])
                        },
                        "status": "pending",
                        "created_at": datetime.now(timezone.utc),
                        "verification_status": "pending",
                        "total_score": 10,
                        "scored": 0,
                        "submitted": False,
                        "attempts_count": 0,
                        "difficulty_level": random.randint(1, 3)
                    }
                    content_items.append(content_item)
                
                # Create content set
                content_set = {
                    "_id": ObjectId(),
                    "user_id": "curated_content_system",
                    "theme_id": theme_id,
                    "input_type": "text",
                    "input_content": f"Curated content set {set_num} for {theme['name_en']}",
                    "tasks": item_ids,
                    "created_at": datetime.now(timezone.utc),
                    "status": "pending",
                    "difficulty_level": random.randint(1, 3),
                    "gentype": "primary",
                    "is_followup": False,
                    "total_tasks": len(item_ids),
                    "attempted_tasks": 0,
                    "total_verified": 0,
                    "total_score": len(item_ids) * 10,
                    "scored": 0,
                    "attempts_count": 0,
                    "metadata": {
                        "set_number": set_num,
                        "theme_name": theme['name'],
                        "theme_name_en": theme['name_en'],
                        "shuffle_questions": True,
                        "shuffle_options": True,
                        "time_limit_minutes": 15
                    }
                }
                
                content_sets.append(content_set)
                all_content_items.extend(content_items)
        
        # Insert content sets
        if content_sets:
            result_sets = await self.db.curated_content_set.insert_many(content_sets)
            print(f"✅ Inserted {len(result_sets.inserted_ids)} content sets")
        
        # Insert content items
        if all_content_items:
            result_items = await self.db.curated_content_items.insert_many(all_content_items)
            print(f"✅ Inserted {len(result_items.inserted_ids)} content items")
        
        return len(content_sets), len(all_content_items)

async def main():
    """Main function to create and populate collections"""
    generator = NepalDataGenerator()
    
    try:
        # Connect to database
        await generator.connect()
        
        # Create collections
        await generator.create_collections()
        
        # Generate and insert themes
        themes = generator.generate_themes()
        theme_ids = await generator.insert_themes(themes)
        
        # Generate and insert content sets and items
        sets_count, items_count = await generator.generate_and_insert_content_sets(theme_ids)
        
        print("\n🎉 Database setup completed successfully!")
        print(f"📊 Summary:")
        print(f"   - Themes: {len(themes)}")
        print(f"   - Content Sets: {sets_count}")
        print(f"   - Content Items: {items_count}")
        
    except Exception as e:
        print(f"❌ Error: {e}")
    finally:
        await generator.close()

if __name__ == "__main__":
    asyncio.run(main())

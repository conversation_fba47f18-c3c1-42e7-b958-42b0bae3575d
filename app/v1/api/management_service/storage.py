"""
Task storage functions for the Task Management Service.
Optimized with parallel threadpool execution and memory-efficient operations.
"""
import asyncio
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import Dict, List, Any, Optional, Tuple, Callable
from datetime import datetime, timezone, timedelta
from bson import ObjectId
from pydantic import BaseModel, Field
import functools
from contextlib import asynccontextmanager
from fastapi import UploadFile

from app.shared.utils.logger import setup_new_logging
from app.shared.db_enums import CollectionName, TaskStatus, TaskResult, QuizType, ScoreValue
from app.shared.utils.mongodb import convert_object_ids
from app.shared.models.user import UserTenantDB
from app.shared.models.task import MinioObject, MediaType
from app.shared.api_errors import ErrorCode, APIError, raise_api_error
from app.shared.api_response import APIResponse
from app.v1.api.management_service.work_compare import compare_audio_files

# Configure logging
loggers = setup_new_logging(__name__)

# Global thread pool for CPU-intensive operations
_thread_pool: Optional[ThreadPoolExecutor] = None
_max_workers = 4  # Configurable based on system resources


def get_thread_pool() -> ThreadPoolExecutor:
    """Get or create the global thread pool."""
    global _thread_pool
    if _thread_pool is None:
        _thread_pool = ThreadPoolExecutor(
            max_workers=_max_workers,
            thread_name_prefix="storage_worker"
        )
    return _thread_pool


async def run_in_thread(func: Callable, *args, **kwargs) -> Any:
    """Run a function in the thread pool."""
    loop = asyncio.get_event_loop()
    executor = get_thread_pool()
    return await loop.run_in_executor(
        executor,
        functools.partial(func, *args, **kwargs)
    )


@asynccontextmanager
async def batch_processor(batch_size: int = 10):
    """Context manager for batch processing operations."""
    batch = []
    try:
        yield batch
    finally:
        if batch:
            # Process remaining items in batch
            await asyncio.gather(*batch, return_exceptions=True)


class TaskSetFilter(BaseModel):
    """Filter parameters for task sets."""
    page: int = Field(1, ge=1, description="Page number")
    limit: int = Field(10, ge=1, le=100, description="Items per page")
    search: Optional[str] = Field(None, description="Search query")
    start_date: Optional[datetime] = Field(None, description="Start date for filtering")
    end_date: Optional[datetime] = Field(None, description="End date for filtering")
    status: Optional[TaskStatus] = Field(None, description="Status filter")
    difficulty_level: Optional[int] = Field(None, ge=1, le=3, description="Difficulty level filter (1=easy, 2=medium, 3=hard)")
    input_type: Optional[str] = Field(None, description="Input type filter (audio, text, video, image)")
    sort_by: str = Field("created_at", description="Field to sort by")
    sort_order: int = Field(-1, ge=-1, le=1, description="Sort order (1 for ascending, -1 for descending)")
    fields_to_retrieve: Optional[List[str]] = Field(None, description="Fields to retrieve")
    user_id: Optional[str] = Field(None, description="Filter by user ID (admin only)")
    gentype: Optional[str] = Field(None, description="Generation type filter (primary, follow_up)")


class TaskManager:
    """
    Optimized task manager with parallel processing capabilities.
    Memory-efficient and high-performance operations.
    """

    def __init__(self, current_user: UserTenantDB):
        """
        Initialize the task manager.

        Args:
            current_user: The current user with tenant information
        """
        self.tenant_db = current_user.async_db
        self.current_user = current_user
        self._cache = {}  # Simple in-memory cache for frequently accessed data

    async def get_task_set(
        self,
        task_set_id: str,
        include_tasks: bool = False,
        fields_to_retrieve: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """
        Get a task set by ID with optimized parallel processing.
        Memory-efficient with concurrent operations.

        Args:
            task_set_id: The task set ID
            include_tasks: Whether to include the full task details
            fields_to_retrieve: Fields to retrieve from the task set

        Returns:
            The task set with selected fields
        """
        try:
            # Validate task_set_id
            if not task_set_id or not ObjectId.is_valid(task_set_id):
                return {"error": "Invalid task set ID"}

            # Check cache first
            cache_key = f"task_set_{task_set_id}_{include_tasks}_{hash(tuple(fields_to_retrieve or []))}"
            if cache_key in self._cache:
                return self._cache[cache_key]

            # Set default fields if none provided
            if fields_to_retrieve is None:
                fields_to_retrieve = [
                    "user_id", "input_type", "input_content", "tasks", "stories",
                    "created_at", "status", "total_score", "scored"
                ]

            # Create projection for MongoDB query
            projection = {field: 1 for field in fields_to_retrieve}
            projection["_id"] = 1  # Always include _id

            # Get the task set
            task_set = await self.tenant_db.task_sets.find_one(
                {"_id": ObjectId(task_set_id)}, projection
            )

            if not task_set:
                return {"error": f"Task set {task_set_id} not found"}

            # Process input_content and tasks in parallel
            async def process_input_content():
                if "input_content" in task_set and isinstance(task_set["input_content"], dict):
                    return await run_in_thread(
                        self._generate_presigned_url,
                        task_set["input_content"]
                    )
                return None

            async def process_tasks():
                if include_tasks and "tasks" in task_set:
                    return await self._get_and_process_tasks_parallel(task_set["tasks"])
                return None

            # Run both operations concurrently
            url_result, tasks_result = await asyncio.gather(
                process_input_content(),
                process_tasks(),
                return_exceptions=True
            )

            # Apply results
            if url_result and not isinstance(url_result, Exception):
                task_set["input_content"]["url"] = url_result

            if tasks_result and not isinstance(tasks_result, Exception):
                task_set["tasks"] = tasks_result

            # Convert MongoDB document to JSON-serializable format
            task_set = convert_object_ids(task_set)

            # Cache the result
            self._cache[cache_key] = task_set

            return task_set
        except Exception as e:
            loggers.error(f"Error getting task set: {str(e)}")
            return {"error": f"Error getting task set: {str(e)}"}

    def _generate_presigned_url(self, input_content: Dict[str, Any]) -> str:
        """Generate presigned URL in thread pool."""
        return self.current_user.minio.get_presigned_url(
            bucket_name=input_content["bucket_name"],
            object_name=input_content["object_path"],
            expires=timedelta(hours=24),
            method="GET"
        )

    async def _get_and_process_tasks_parallel(self, task_ids: List[Any]) -> List[Dict[str, Any]]:
        """Get and process tasks with parallel processing."""
        if not task_ids:
            return []

        # Convert task IDs to ObjectIds
        object_ids = [ObjectId(tid) if isinstance(tid, str) else tid for tid in task_ids]

        # Get tasks from database
        tasks = await self.tenant_db.task_items.find(
            {"_id": {"$in": object_ids}}
        ).to_list(length=len(object_ids))

        # Process tasks in parallel using thread pool
        async def process_single_task(task):
            return await run_in_thread(self._process_task_format, task)

        # Process all tasks concurrently
        processed_tasks = await asyncio.gather(
            *[process_single_task(task) for task in tasks],
            return_exceptions=True
        )

        # Filter out exceptions and return valid results
        return [task for task in processed_tasks if not isinstance(task, Exception)]

    def _process_task_format(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """Process task format in thread pool to avoid blocking."""
        # Convert ObjectIds to strings
        task = convert_object_ids(task)

        # Ensure question structure is properly formatted
        if "question" in task and isinstance(task["question"], dict):
            question = task["question"]

            # Convert options format if needed
            if "options" in question and question["options"]:
                if isinstance(question["options"], list):
                    options_dict = {}
                    for i, option in enumerate(question["options"]):
                        key = chr(ord('a') + i)  # a, b, c, d, e...
                        options_dict[key] = option
                    question["options"] = options_dict

            # Ensure all expected question fields exist
            question.setdefault("text", "")
            question.setdefault("translated_text", "")
            question.setdefault("answer_hint", "")
            question.setdefault("media_url", None)

        return task

    async def get_task_by_id(
        self, task_id: str, fields_to_retrieve: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """
        Get a task by ID with control over what fields are returned.
        Updated to handle the new task structure with key-value options.

        Args:
            task_id: The task ID
            fields_to_retrieve: Fields to retrieve from the task

        Returns:
            The task with selected fields
        """
        try:
            # Validate task_id
            if not task_id or not ObjectId.is_valid(task_id):
                return {"error": "Invalid task ID"}

            # Set default fields if none provided
            if fields_to_retrieve is None:
                fields_to_retrieve = [
                    "type",
                    "question",
                    "correct_answer",
                    "user_answer",
                    "status",
                    "result",
                    "total_score",  # Use 'total_score' consistently
                    "scored",  # Use 'scored' consistently
                    "submitted",  # Include submission status
                    "submitted_at",  # Include submission timestamp
                    "complexity",
                    "is_attempted",
                    "attempts_count"
                ]

            # Create projection for MongoDB query
            projection = {field: 1 for field in fields_to_retrieve}
            projection["_id"] = 1  # Always include _id

            # Get the task
            task = await self.tenant_db.task_items.find_one(
                {"_id": ObjectId(task_id)}, projection
            )

            if not task:
                return {"error": f"Task {task_id} not found"}

            # Convert MongoDB document to JSON-serializable format
            task = convert_object_ids(task)

            # Ensure question structure is properly formatted for the new schema
            if "question" in task and isinstance(task["question"], dict):
                question = task["question"]

                # Ensure options are in key-value format if they exist
                if "options" in question and question["options"]:
                    if isinstance(question["options"], list):
                        # Convert old list format to new key-value format
                        options_dict = {}
                        for i, option in enumerate(question["options"]):
                            key = chr(ord('a') + i)  # a, b, c, d, e...
                            options_dict[key] = option
                        question["options"] = options_dict
                    # If already dict format, keep as is

                # Ensure all expected question fields exist
                question.setdefault("text", "")
                question.setdefault("translated_text", "")
                question.setdefault("answer_hint", "")
                question.setdefault("media_url", None)

            return task
        except Exception as e:
            loggers.error(f"Error getting task: {str(e)}")
            return {"error": f"Error getting task: {str(e)}"}

    async def get_filtered_task_sets(
        self,
        filter_params: TaskSetFilter
    ) -> Dict[str, Any]:
        """
        Get task sets with filtering, sorting, and pagination using MongoDB aggregation.

        Args:
            filter_params: TaskSetFilter object containing all filter parameters

        Returns:
            Dictionary with data and pagination metadata in PaginationResponse format
        """
        try:
            # Calculate skip value for pagination
            skip = (filter_params.page - 1) * filter_params.limit

            # Build match stage for aggregation pipeline
            match_stage = {}

            # Add search filter if provided
            if filter_params.search:
                match_stage["$or"] = [
                    {"user_id": {"$regex": filter_params.search, "$options": "i"}},
                    {"input_type": {"$regex": filter_params.search, "$options": "i"}}
                ]

            # Filter by user_id - for admin users, they can see all or filter by specific user
            # For non-admin users, they can only see their own task sets
            current_user_id = str(self.current_user.user.id)

            if self.current_user.user.role == "admin":
                # Admin can see all task sets or filter by specific user
                if filter_params.user_id:
                    # Try both ObjectId and string formats for user_id
                    try:
                        match_stage["user_id"] = ObjectId(filter_params.user_id)
                    except (ValueError, TypeError):
                        match_stage["user_id"] = filter_params.user_id
                # If no user_id specified, admin sees all task sets (no user_id filter)
            else:
                # Non-admin users only see their own task sets
                # Try both ObjectId and string formats for current user
                try:
                    match_stage["user_id"] = ObjectId(current_user_id)
                except (ValueError, TypeError):
                    match_stage["user_id"] = current_user_id

            # Add date filter if provided
            if filter_params.start_date or filter_params.end_date:
                date_filter = {}
                if filter_params.start_date:
                    date_filter["$gte"] = filter_params.start_date
                if filter_params.end_date:
                    date_filter["$lte"] = filter_params.end_date
                if date_filter:
                    match_stage["created_at"] = date_filter

            # Add status filter if provided
            if filter_params.status:
                match_stage["status"] = filter_params.status

            # Add difficulty level filter if provided
            if filter_params.difficulty_level is not None:
                match_stage["difficulty_level"] = filter_params.difficulty_level

            # Add input type filter if provided
            if filter_params.input_type:
                match_stage["input_type"] = filter_params.input_type
            if filter_params.gentype:
                match_stage["gentype"] = filter_params.gentype

            # Set default fields if none provided
            if filter_params.fields_to_retrieve is None:
                filter_params.fields_to_retrieve = [
                    "title",
                    "user_id",
                    "input_type",
                    # "input_content",
                    "tasks",
                    "created_at",
                    "status",
                    "total_score",
                    "scored",
                    "total_tasks",
                    "source",
                    "difficulty_level",
                    "attempted_tasks"
                ]

            # Create projection for MongoDB aggregation
            projection = {"_id": 0, "id": "$_id"}
            for field in filter_params.fields_to_retrieve:
                projection[field] = 1

            # Add a special projection for user_id to convert ObjectId to string
            if "user_id" in filter_params.fields_to_retrieve:
                projection["user_id"] = {"$toString": "$user_id"}

            # Add a special projection for tasks to convert ObjectIds to strings
            if "tasks" in filter_params.fields_to_retrieve:
                projection["tasks"] = {
                    "$map": {
                        "input": "$tasks",
                        "as": "task",
                        "in": {"$toString": "$$task"}
                    }
                }

            # Add a special projection for input_type to handle 'transcription' value
            if "input_type" in filter_params.fields_to_retrieve:
                projection["input_type"] = {
                    "$cond": {
                        "if": {"$eq": ["$input_type", "transcription"]},
                        "then": "text",  # Convert 'transcription' to 'text'
                        "else": "$input_type"
                    }
                }

            # Add score field mapping - ensure scored field is present
            if "scored" in filter_params.fields_to_retrieve:
                projection["scored"] = {"$ifNull": ["$scored", 0]}

            # Add total_score field mapping
            if "total_score" in filter_params.fields_to_retrieve:
                projection["total_score"] = {"$ifNull": ["$total_score", 0]}

            # Add counter fields
            if "attempted_tasks" in filter_params.fields_to_retrieve:
                projection["attempted_tasks"] = {"$ifNull": ["$attempted_tasks", 0]}

            if "total_completed" in filter_params.fields_to_retrieve:
                projection["total_completed"] = {"$ifNull": ["$total_completed", 0]}

            if "attempts_count" in filter_params.fields_to_retrieve:
                projection["attempts_count"] = {"$ifNull": ["$attempts_count", 0]}

            # Build the aggregation pipeline
            pipeline = [
                # Match stage
                {"$match": match_stage},

                # Sort stage
                {"$sort": {filter_params.sort_by: filter_params.sort_order}},

                # Facet stage for pagination
                {"$facet": {
                    "metadata": [
                        {"$count": "total"}
                    ],
                    "data": [
                        {"$skip": skip},
                        {"$limit": filter_params.limit},
                        {"$project": projection}
                    ]
                }}
            ]

            # Execute the aggregation pipeline
            cursor = await self.tenant_db.task_sets.aggregate(pipeline)
            result_list = await cursor.to_list(length=1)

            # Extract data and metadata
            result = result_list[0] if result_list else {"metadata": [], "data": []}

            # Get total count
            total = result["metadata"][0]["total"] if result["metadata"] else 0

            # Calculate total pages
            total_pages = (total + filter_params.limit - 1) // filter_params.limit if total > 0 else 0

            # Return in PaginationResponse format
            return {
                "data": result["data"],
                "meta": {
                    "page": filter_params.page,
                    "limit": filter_params.limit,
                    "total": total,
                    "total_pages": total_pages
                }
            }
        except Exception as e:
            loggers.error(f"Error getting filtered task sets: {str(e)}")
            return {"error": f"Error getting filtered task sets: {str(e)}"}

    async def get_task_set_filter_values(self) -> Dict[str, Any]:
        """
        Get actual filter values from the database for task sets.

        This method queries the database to get unique values that actually exist
        for each filterable field, which is useful for dynamic filter population.

        Returns:
            Dictionary with actual values found in the database
        """
        try:
            current_user_id = str(self.current_user.user.id)

            # Build base match stage for user filtering
            base_match = {}
            if self.current_user.user.role != "admin":
                # Non-admin users only see their own task sets
                try:
                    base_match["user_id"] = ObjectId(current_user_id)
                except (ValueError, TypeError):
                    base_match["user_id"] = current_user_id

            # Get unique values for each filterable field
            pipeline_status = [
                {"$match": base_match},
                {"$group": {"_id": "$status"}},
                {"$sort": {"_id": 1}}
            ]

            pipeline_difficulty = [
                {"$match": base_match},
                {"$match": {"difficulty_level": {"$exists": True, "$ne": None}}},
                {"$group": {"_id": "$difficulty_level"}},
                {"$sort": {"_id": 1}}
            ]

            pipeline_input_type = [
                {"$match": base_match},
                {"$group": {"_id": "$input_type"}},
                {"$sort": {"_id": 1}}
            ]

            pipeline_source = [
                {"$match": base_match},
                {"$match": {"source": {"$exists": True, "$ne": None}}},
                {"$group": {"_id": "$source"}},
                {"$sort": {"_id": 1}}
            ]

            # Execute aggregation pipelines
            status_cursor = await self.tenant_db.task_sets.aggregate(pipeline_status)
            difficulty_cursor = await self.tenant_db.task_sets.aggregate(pipeline_difficulty)
            input_type_cursor = await self.tenant_db.task_sets.aggregate(pipeline_input_type)
            source_cursor = await self.tenant_db.task_sets.aggregate(pipeline_source)

            # Convert cursors to lists
            status_values = [doc["_id"] for doc in await status_cursor.to_list(length=None) if doc["_id"]]
            difficulty_values = [doc["_id"] for doc in await difficulty_cursor.to_list(length=None) if doc["_id"]]
            input_type_values = [doc["_id"] for doc in await input_type_cursor.to_list(length=None) if doc["_id"]]
            source_values = [doc["_id"] for doc in await source_cursor.to_list(length=None) if doc["_id"]]

            # Get date range
            date_pipeline = [
                {"$match": base_match},
                {"$group": {
                    "_id": None,
                    "min_date": {"$min": "$created_at"},
                    "max_date": {"$max": "$created_at"}
                }}
            ]

            date_cursor = await self.tenant_db.task_sets.aggregate(date_pipeline)
            date_result = await date_cursor.to_list(length=1)
            date_range = date_result[0] if date_result else {"min_date": None, "max_date": None}

            return {
                "status_values": status_values,
                "difficulty_values": difficulty_values,
                "input_type_values": input_type_values,
                "source_values": source_values,
                "date_range": {
                    "min_date": date_range.get("min_date"),
                    "max_date": date_range.get("max_date")
                }
            }

        except Exception as e:
            loggers.error(f"Error getting task set filter values: {str(e)}")
            return {"error": f"Error getting task set filter values: {str(e)}"}

    async def submit_task_set(
        self, task_set_id: str, user_id: str, answers: List[Dict[str, Any]],
        current_user: UserTenantDB
    ) -> Dict[str, Any]:
        """
        Submit answers for a task set with optimized parallel processing.
        Memory-efficient batch operations for high performance.

        Args:
            task_set_id: The task set ID
            user_id: The user ID
            answers: The list of answers

        Returns:
            Dictionary with submission_id and scores
        """
        try:
            # Get the task set
            task_set = await self.get_task_set(
                task_set_id=task_set_id,
                include_tasks=True,
                fields_to_retrieve=["user_id", "input_type", "tasks", "created_at", "total_score", "total_completed", "total_tasks"]
            )

            if "error" in task_set:
                return task_set

            # Verify the task set belongs to the user
            if task_set.get("user_id") != user_id:
                return {"error": "You do not have permission to submit answers for this task set"}

            # Get the tasks
            tasks = task_set.get("tasks", [])
            if not tasks:
                return {"error": "No tasks found in the task set"}

            # Create a lookup for tasks by ID
            task_lookup = {task["id"]: task for task in tasks}

            # Process answers in parallel batches for better performance
            scores, total_points_earned = await self._process_answers_parallel(
                answers, task_lookup
            )

            # Batch update all task items
            await self._batch_update_task_items(answers, scores, task_lookup)

            # Update task set status
            await self._update_task_set_completion(
                task_set_id, total_points_earned, len(answers), task_set,current_user
            )

            # Create task history
            submission_id = await self._create_task_history(
                task_set_id, user_id, task_set, tasks, answers, scores, total_points_earned
            )

            loggers.info(f"Saved task submission {submission_id} for user {user_id}, points: {total_points_earned}/{task_set.get('total_score', 0)}")

            return {
                "submission_id": submission_id,
                "scores": scores,
                "total_score": task_set.get("total_score", 0),
                "score": total_points_earned,
                "task_set_id": task_set_id
            }
        except Exception as e:
            loggers.error(f"Error submitting task set: {str(e)}")
            return {"error": f"Error submitting task set: {str(e)}"}

    async def _process_answers_parallel(
        self, answers: List[Dict[str, Any]], task_lookup: Dict[str, Any]
    ) -> Tuple[List[int], int]:
        """Process answers in parallel for better performance."""
        async def process_single_answer(answer_data):
            return await run_in_thread(
                self._score_single_answer, answer_data, task_lookup
            )

        # Process all answers concurrently
        results = await asyncio.gather(
            *[process_single_answer(answer_data) for answer_data in answers],
            return_exceptions=True
        )

        # Extract scores and calculate total
        scores = []
        total_points_earned = 0
        for result in results:
            if isinstance(result, Exception):
                scores.append(0)
            else:
                score = result.get("points_earned", 0)
                scores.append(score)
                total_points_earned += score

        return scores, total_points_earned

    def _score_single_answer(
        self, answer_data: Dict[str, Any], task_lookup: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Score a single answer in thread pool."""
        task_id = answer_data.get("task_id")
        answer = answer_data.get("answer")
        task_type = answer_data.get("task_type")

        if not task_id or task_id not in task_lookup:
            return {"points_earned": 0, "feedback": "Task not found"}

        task = task_lookup[task_id]
        question = task.get("question", {})

        # Get correct answer
        correct_answer_value = None
        if "answer" in question:
            correct_answer_value = question["answer"]
        elif "correct_answer" in task:
            correct_answer = task.get("correct_answer", {})
            correct_answer_value = correct_answer.get("value") if isinstance(correct_answer, dict) else None

        # Score based on task type
        if task_type == QuizType.MULTIPLE_CHOICE:
            answer_value = answer.get("value", []) if isinstance(answer, dict) else answer
            if isinstance(answer_value, list) and isinstance(correct_answer_value, list):
                is_correct = set(answer_value) == set(correct_answer_value)
                points_earned = ScoreValue.MULTIPLE_CHOICE if is_correct else 0
                options = question.get("options", {})
                correct_options_text = [options.get(key, key) for key in correct_answer_value if key in options]
                feedback = "All correct options selected!" if is_correct else f"Incorrect. The correct answers are: {', '.join(correct_options_text)}"
            else:
                points_earned = 0
                feedback = "Invalid answer format for multiple choice question"
        elif task_type == QuizType.SINGLE_CHOICE:
            answer_value = answer.get("value") if isinstance(answer, dict) else answer
            is_correct = answer_value == correct_answer_value
            points_earned = ScoreValue.SINGLE_CHOICE if is_correct else 0
            options = question.get("options", {})
            correct_option_text = options.get(correct_answer_value, correct_answer_value) if correct_answer_value in options else correct_answer_value
            feedback = "Correct! Well done." if is_correct else f"Incorrect. The correct answer is: {correct_option_text}"
        elif task_type == QuizType.IMAGE_IDENTIFICATION:
            answer_value = answer.get("value") if isinstance(answer, dict) else answer
            is_correct = answer_value == correct_answer_value
            points_earned = ScoreValue.IMAGE_IDENTIFICATION if is_correct else 0
            feedback = "Correct identification!" if is_correct else f"Incorrect. The correct answer is: {correct_answer_value}"
        elif task_type == QuizType.SPEAK_WORD:
            is_correct = True  # Simplified implementation
            points_earned = ScoreValue.SPEAK_WORD if is_correct else 0
            feedback = "Pronunciation accepted!" if is_correct else "Pronunciation needs improvement."
        elif task_type == QuizType.ANSWER_IN_WORD:
            answer_value = answer.get("value") if isinstance(answer, dict) else answer
            is_correct = str(answer_value).lower() == str(correct_answer_value).lower()
            points_earned = ScoreValue.ANSWER_IN_WORD if is_correct else 0
            feedback = "Correct answer!" if is_correct else f"Not quite. The correct answer is: {correct_answer_value}"
        else:
            return {"points_earned": 0, "feedback": f"Unsupported task type: {task_type}"}

        return {
            "points_earned": points_earned,
            "feedback": feedback,
            "task_id": task_id,
            "answer": answer
        }

    async def _batch_update_task_items(
        self, answers: List[Dict[str, Any]], scores: List[int], task_lookup: Dict[str, Any]
    ) -> None:
        """Batch update task items for better performance."""
        updates = []
        for i, answer_data in enumerate(answers):
            task_id = answer_data.get("task_id")
            if task_id and task_id in task_lookup:
                update_data = {
                    "status": TaskStatus.COMPLETED,
                    "result": TaskResult.CORRECT if scores[i] > 0 else TaskResult.INCORRECT,
                    "user_answer": answer_data.get("answer"),
                    "answered_at": datetime.now(timezone.utc),
                    "scored": scores[i],
                    "is_attempted": True,
                    "submitted": True,
                    "submitted_at": datetime.now(timezone.utc)
                }
                updates.append({
                    "filter": {"_id": ObjectId(task_id)},
                    "update": {"$set": update_data}
                })

        # Execute batch updates
        if updates:
            async with batch_processor() as batch:
                for update in updates:
                    batch.append(
                        self.tenant_db.task_items.update_one(
                            update["filter"], update["update"]
                        )
                    )

    async def _update_task_set_completion(
        self, task_set_id: str, total_points_earned: int, answers_count: int, task_set: Dict[str, Any],
        current_user: UserTenantDB
    ) -> None:
        """Update task set completion status."""

        # Update task set status to completed
        await self.tenant_db.task_sets.update_one(
            {"_id": ObjectId(task_set_id)},
            {
                "$set": {
                    "status": TaskStatus.COMPLETED,
                    "completed_at": datetime.now(timezone.utc),
                    "scored": total_points_earned,
                    "attempted_tasks": answers_count,
                    "remark": f"Completed with score {total_points_earned}/{task_set.get('total_score', 0)}"
                }
            }
        )

        # Check if this is a primary task set and trigger followup generation
        gen_type = task_set.get("gentype", "primary")
        if gen_type == "primary":
            try:
                # Import followup function directly to avoid circular imports
                from app.v2.api.socket_service_v2.generator.folllowup import followup_generate

                
                await followup_generate(task_set_id, current_user)
                loggers.info(f"Triggered followup generation for completed task set {task_set_id}")
            except Exception as e:
                loggers.error(f"Error triggering followup generation for task set {task_set_id}: {e}")
                # Don't fail the completion if followup generation fails

    async def _create_task_history(
        self, task_set_id: str, user_id: str, task_set: Dict[str, Any],
        tasks: List[Dict[str, Any]], answers: List[Dict[str, Any]],
        scores: List[int], total_points_earned: int
    ) -> str:
        """Create task history record."""
        task_history = {
            "task_set_id": task_set_id,
            "user_id": user_id,
            "input_type": task_set.get("input_type"),
            "tasks": tasks,
            "answers": answers,
            "scores": scores,
            "total_score": task_set.get("total_score", 0),
            "scored": total_points_earned,
            "created_at": task_set.get("created_at"),
            "submitted_at": datetime.now(timezone.utc),
            "remark": f"Submission for task set {task_set_id}"
        }

        result = await self.tenant_db.task_history.insert_one(task_history)
        return str(result.inserted_id)

    def clear_cache(self) -> None:
        """Clear the internal cache."""
        self._cache.clear()

    @classmethod
    def cleanup_thread_pool(cls) -> None:
        """Cleanup the global thread pool."""
        global _thread_pool
        if _thread_pool:
            _thread_pool.shutdown(wait=True)
            _thread_pool = None

    async def submit_task_item(
        self, task_id: str, answer: Any, task_type: QuizType, folder: Optional[str] = None    ) -> Dict[str, Any]:
        """
        Submit an answer for a single task item with standardized scoring logic.

        Args:
            task_id: The task ID to submit answer for
            answer: The user's answer (format depends on task type)
            task_type: The type of task (SINGLE_CHOICE, MULTIPLE_CHOICE, etc.)
            folder: Optional folder for file storage (deprecated)

        Returns:
            Dictionary with comprehensive submission result information including:
            - task_id: The submitted task ID
            - is_correct: Whether the answer was correct
            - scored: Points earned for this submission
            - total_score: Maximum possible points for this task
            - feedback: Detailed feedback message
            - is_first_attempt: Whether this was the first submission attempt
            - correct_answer: Object with correct answer value and display text
            - user_answer: The submitted answer
            - task_type: Type of the task
            - question_text: The question text
            - options: Available options (for choice questions)

        Raises:
            APIError: If the task is not found, already completed, or other validation errors
        """
        try:
            # Step 1: Validate and retrieve task
            task = await self._get_and_validate_task(task_id)

            # Step 2: Extract task information
            task_info = self._extract_task_info(task)

            # Step 3: Calculate score based on answer and task type
            scoring_result = await self._calculate_score(answer, task_info, task_type, folder)

            # Step 4: Update task item in database
            await self._update_task_item(task_id, task_info, scoring_result)

            # Step 5: Update task set scores
            await self._update_task_set_scores(task_id, task_info, scoring_result)

            # Step 6: Return standardized result with correct answer
            return self._build_submission_result(task_id, task_info, scoring_result)

        except APIError:
            raise
        except Exception as e:
            loggers.error(f"Error submitting task item {task_id}: {str(e)}")
            return {"error": f"Error submitting task item: {str(e)}"}

    async def _get_and_validate_task(self, task_id: str) -> Dict[str, Any]:
        """Get task and validate it can be submitted."""
        task = await self.get_task_by_id(
            task_id=task_id,
            fields_to_retrieve=[
                "type", "correct_answer", "question", "status", "user_answer",
                "is_attempted", "total_score", "scored", "submitted", "submitted_at",
                "complexity", "attempts_count","task_set_id"
            ]
        )

        if "error" in task:
            error_msg = task.get("error", "Task not found")
            raise APIError(
                error_code=ErrorCode.TASK_NOT_FOUND,
                message=error_msg,
                status_code=404
            )

        # Allow re-submission but track if it's first attempt
        # Remove the completed check to allow score improvements
        return task

    def _extract_task_info(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """Extract and organize task information for processing."""
        question = task.get("question", {})

        # Get correct answer from either new or old format
        correct_answer_value = None
        if "answer" in question:
            correct_answer_value = question["answer"]
        elif "correct_answer" in task:
            correct_answer = task.get("correct_answer", {})
            correct_answer_value = correct_answer.get("value") if isinstance(correct_answer, dict) else None

        return {
            "task": task,
            "question": question,
            "correct_answer": correct_answer_value,
            "total_score": task.get("total_score", 10),
            "is_first_attempt": not task.get("submitted", False),
            "current_attempts": task.get("attempts_count", 0),
            "options": question.get("options", {})
        }

    async def _calculate_score(self, answer: Any, task_info: Dict[str, Any], task_type: QuizType, folder:str) -> Dict[str, Any]:
        """Calculate score based on answer and task type with partial scoring for multiple choice."""
        user_answer_value = answer.value if hasattr(answer, 'value') else answer
        correct_answer = task_info["correct_answer"]
        options = task_info["options"]

        # Get scoring result based on task type
        if task_type == QuizType.MULTIPLE_CHOICE:
            result = self._score_multiple_choice(user_answer_value, correct_answer, options)
        elif task_type == QuizType.SINGLE_CHOICE:
            result = self._score_single_choice(user_answer_value, correct_answer, options)
        elif task_type == QuizType.IMAGE_IDENTIFICATION:
            result = self._score_image_identification(user_answer_value, correct_answer)
        elif task_type == QuizType.SPEAK_WORD:
            # Get answer_hint from task_info for speak_word tasks
            answer_hint = task_info.get("question", {}).get("answer_hint", "")
            result = await self._score_speak_word(answer, folder, answer_hint)
        elif task_type == QuizType.ANSWER_IN_WORD:
            result = self._score_answer_in_word(user_answer_value, correct_answer)
        else:
            return {"error": f"Unsupported task type: {task_type}"}

        # Add the original answer to the result
        result["user_answer"] = answer
        return result

    def _score_multiple_choice(self, user_answer: Any, correct_answer: Any, options: Dict) -> Dict[str, Any]:
        """Score multiple choice with partial credit."""
        if not isinstance(user_answer, list) or not isinstance(correct_answer, list):
            return {
                "is_correct": False,
                "score_earned": 0,
                "feedback": "Invalid answer format for multiple choice question"
            }

        user_set = set(user_answer)
        correct_set = set(correct_answer)

        # Calculate partial score
        correct_selections = len(user_set & correct_set)  # Correct answers selected
        incorrect_selections = len(user_set - correct_set)  # Wrong answers selected

        total_correct = len(correct_set)
        max_score = ScoreValue.MULTIPLE_CHOICE

        # Partial scoring: (correct - incorrect) / total_correct * max_score
        # Minimum score is 0
        score_ratio = max(0, (correct_selections - incorrect_selections) / total_correct)
        score_earned = int(score_ratio * max_score)

        is_correct = user_set == correct_set

        # Generate feedback
        if is_correct:
            feedback = "Perfect! All correct options selected."
        else:
            correct_options_text = [options.get(key, key) for key in correct_answer if key in options]
            feedback = f"Partial credit: {score_earned}/{max_score}. Correct answers: {', '.join(correct_options_text)}"

        return {
            "is_correct": is_correct,
            "score_earned": score_earned,
            "feedback": feedback
        }

    def _score_single_choice(self, user_answer: Any, correct_answer: Any, options: Dict) -> Dict[str, Any]:
        """Score single choice question."""
        is_correct = user_answer == correct_answer
        score_earned = ScoreValue.SINGLE_CHOICE if is_correct else 0

        if is_correct:
            feedback = "Correct! Well done."
        else:
            correct_option_text = options.get(correct_answer, correct_answer) if correct_answer in options else correct_answer
            feedback = f"Incorrect. The correct answer is: {correct_option_text}"

        return {
            "is_correct": is_correct,
            "score_earned": score_earned,
            "feedback": feedback
        }

    def _score_image_identification(self, user_answer: Any, correct_answer: Any) -> Dict[str, Any]:
        """Score image identification question."""
        is_correct = user_answer == correct_answer
        score_earned = ScoreValue.IMAGE_IDENTIFICATION if is_correct else 0

        feedback = "Correct identification!" if is_correct else f"Incorrect. The correct answer is: {correct_answer}"

        return {
            "is_correct": is_correct,
            "score_earned": score_earned,
            "feedback": feedback
        }

    async def _score_speak_word(self, answer: Any, folder: str, answer_hint: str = "") -> Dict[str, Any]:
        """
        Score speak word question (audio submission) using Gemini for verification.
        
        Args:
            answer: The user's answer (not used in this implementation)
            folder: The folder/object_name where the audio file is stored
            answer_hint: The expected word/phrase for pronunciation comparison
            
        Returns:
            Dictionary containing:
            - is_correct: bool - Whether pronunciation was correct
            - score_earned: int - Points earned for this submission
            - feedback: str - Detailed feedback about the pronunciation
            - pronunciation_details: dict - Additional details from Gemini including:
                - word: str - The word being evaluated
                - score: int - Pronunciation score (1-10)
                - pronunciation_rating: str - Rating of pronunciation
                - reason: str - Explanation of the rating
                - token_usage: dict - Token usage information
        """
        # Check if a file was uploaded directly
        if folder is not None:
            try:
                # Use Gemini to compare the audio file with the expected word
                gemini_response = await compare_audio_files(folder, answer_hint, self.current_user)
                
                # Extract the response data with defaults
                is_correct = gemini_response.get("is_correct", True)
                score = gemini_response.get("Score", 5)  # Default to 5 if not provided
                
                # Calculate score_earned based on the pronunciation score (scale 1-10 to 0-ScoreValue.SPEAK_WORD)
                score_earned = int((score / 10) * ScoreValue.SPEAK_WORD)
                
                # Prepare feedback
                feedback = (
                    gemini_response.get("reason", "") or 
                    ("Pronunciation accepted!" if is_correct else f"Pronunciation needs improvement for: {answer_hint}")
                )
                
                # Include all the details from Gemini in the response
                pronunciation_details = {
                    "word": gemini_response.get("word", answer_hint),
                    "score": score,
                    "pronunciation_rating": gemini_response.get("pronunciation_rating", "Fair"),
                    "reason": gemini_response.get("reason", ""),
                    "token_usage": gemini_response.get("token_usage", {})
                }
                
            except Exception as e:
                loggers.error(f"Error comparing audio files: {e}")
                # Fallback to accepting the submission if comparison fails
                is_correct = True
                score_earned = ScoreValue.SPEAK_WORD
                feedback = "Audio file received (verification unavailable)"
                pronunciation_details = {
                    "word": answer_hint,
                    "score": 5,
                    "pronunciation_rating": "Fair",
                    "reason": "Error during pronunciation evaluation",
                    "token_usage": {}
                }
        # Check if the answer contains media objects (legacy format)
        elif hasattr(answer, 'media') and answer.media and isinstance(answer.media, list) and len(answer.media) > 0:
            # For legacy format, we'll assume the submission is correct
            is_correct = True
            score_earned = ScoreValue.SPEAK_WORD
            feedback = "Pronunciation accepted!"
            pronunciation_details = {
                "word": answer_hint,
                "score": 10,  # Full score for legacy format
                "pronunciation_rating": "Excellent",
                "reason": "Legacy format - pronunciation accepted without verification",
                "token_usage": {}
            }
        else:
            is_correct = False
            score_earned = 0
            feedback = "No audio recording provided"
            pronunciation_details = {
                "word": answer_hint,
                "score": 0,
                "pronunciation_rating": "Incorrect",
                "reason": "No audio recording was provided",
                "token_usage": {}
            }

        return {
            "is_correct": is_correct,
            "score_earned": score_earned,
            "feedback": feedback,
            "pronunciation_details": pronunciation_details
        }

    def _score_answer_in_word(self, user_answer: Any, correct_answer: Any) -> Dict[str, Any]:
        """Score answer in word question."""
        is_correct = str(user_answer).lower() == str(correct_answer).lower()
        score_earned = ScoreValue.ANSWER_IN_WORD if is_correct else 0

        feedback = "Correct answer!" if is_correct else f"Not quite. The correct answer is: {correct_answer}"

        return {
            "is_correct": is_correct,
            "score_earned": score_earned,
            "feedback": feedback
        }

    async def _update_task_item(self, task_id: str, task_info: Dict[str, Any], scoring_result: Dict[str, Any]) -> None:
        """Update task item with submission results."""
        if "error" in scoring_result:
            return

        result_value = TaskResult.CORRECT if scoring_result["is_correct"] else TaskResult.INCORRECT
        score_earned = scoring_result["score_earned"]
        is_first_attempt = task_info["is_first_attempt"]

        # Get old score for task set update
        current_task = await self.tenant_db.task_items.find_one(
            {"_id": ObjectId(task_id)},
            {"scored": 1, "attempts_count": 1}
        )

        old_score = current_task.get("scored", 0) if current_task else 0
        old_attempts = current_task.get("attempts_count", 0) if current_task else 0

        # Store old score in task_info for task set update
        task_info["old_score"] = old_score

        update_data = {
            "status": TaskStatus.COMPLETED,
            "result": result_value,
            "user_answer": scoring_result.get("user_answer"),
            "answered_at": datetime.now(timezone.utc),
            "scored": score_earned,
            "total_score": task_info["total_score"],
            "is_attempted": True,
            "attempts_count": old_attempts + 1,
            "remark": scoring_result["feedback"],
            "updated_at": datetime.now(timezone.utc)
        }

        # Set submitted fields on first submission
        if is_first_attempt:
            update_data["submitted"] = True
            update_data["submitted_at"] = datetime.now(timezone.utc)

        # Update the task item
        result = await self.tenant_db.task_items.update_one(
            {"_id": ObjectId(task_id)},
            {"$set": update_data}
        )

        if result.modified_count > 0:
            loggers.info(f"✅ Updated task item {task_id}: {old_score} -> {score_earned}")
        else:
            loggers.warning(f"Failed to update task item {task_id}")

    async def _update_task_set_scores(self, task_id: str, task_info: Dict[str, Any], scoring_result: Dict[str, Any]) -> None:
        """Simple task set score update - just add/subtract score changes."""
        if "error" in scoring_result:
            return

        # Find the task set with more fields for completion check
        task_set = await self.tenant_db.task_sets.find_one(
            {"tasks": task_id},
            {"_id": 1, "attempted_tasks": 1, "scored": 1, "total_tasks": 1, "gentype": 1, "input_content": 1, "user_id": 1}
        )

        if not task_set:
            loggers.warning(f"No task set found for task {task_id}")
            return

        score_earned = scoring_result.get("score_earned", 0)
        is_first_attempt = task_info.get("is_first_attempt", True)

        # Get old score from task item (before it was updated)
        old_score = task_info.get("old_score", 0)
        score_change = score_earned - old_score

        # Update task set score
        new_scored = task_set.get("scored", 0) + score_change
        new_attempted_tasks = task_set.get("attempted_tasks", 0)

        update_data = {
            "scored": new_scored,
            "updated_at": datetime.now(timezone.utc)
        }

        # Only increment attempted_tasks on first submission
        if is_first_attempt:
            new_attempted_tasks += 1
            update_data["attempted_tasks"] = new_attempted_tasks

        await self.tenant_db.task_sets.update_one(
            {"_id": task_set["_id"]},
            {"$set": update_data}
        )

        loggers.info(f"Updated task set: score change {score_change}, new total: {new_scored}")





    def _build_submission_result(self, task_id: str, task_info: Dict[str, Any], scoring_result: Dict[str, Any]) -> Dict[str, Any]:
        """
        Build standardized submission result with correct answer information.
        
        Args:
            task_id: The ID of the task being submitted
            task_info: Dictionary containing task information
            scoring_result: Dictionary containing scoring results including pronunciation_details
            
        Returns:
            Dictionary containing comprehensive submission result including:
            - task_id: The ID of the task
            - is_correct: Whether the answer was correct
            - scored: Points earned for this submission
            - total_score: Maximum possible points for this task
            - feedback: Detailed feedback message
            - is_first_attempt: Whether this was the first submission attempt
            - correct_answer: Object with correct answer value and display text
            - user_answer: The submitted answer
            - task_type: Type of the task
            - question_text: The question text
            - options: Available options (for choice questions)
            - pronunciation_details: Detailed pronunciation analysis (for SPEAK_WORD tasks)
        """
        if "error" in scoring_result:
            return scoring_result

        # Build comprehensive correct answer information with safe access
        correct_answer_info = {
            "value": task_info.get("correct_answer"),
            "display_text": self._get_correct_answer_display_text(task_info)
        }

        # Build the base result dictionary with all available information
        result = {
            "task_id": str(task_id),
            "is_correct": scoring_result.get("is_correct", False),
            "scored": scoring_result.get("score_earned", 0),
            "total_score": task_info.get("total_score", 0),
            "feedback": scoring_result.get("feedback", "No feedback available"),
            "is_first_attempt": task_info.get("is_first_attempt", True),
            "correct_answer": correct_answer_info,
            "user_answer": scoring_result.get("user_answer"),
            "task_type": task_info.get("task", {}).get("type"),
            "question_text": task_info.get("question", {}).get("text", ""),
            "options": task_info.get("options") if task_info.get("options") else None
        }
        
        # Add pronunciation details if available (for SPEAK_WORD tasks)
        if "pronunciation_details" in scoring_result:
            result["pronunciation_details"] = scoring_result["pronunciation_details"]
            
        return result

    def _get_correct_answer_display_text(self, task_info: Dict[str, Any]) -> str:
        """Get human-readable display text for the correct answer."""
        correct_answer = task_info.get("correct_answer")
        options = task_info.get("options", {})

        if not correct_answer:
            return "No correct answer available"

        # For multiple choice questions with options
        if options and isinstance(correct_answer, list):
            # Multiple choice - show all correct option texts
            correct_texts = []
            for key in correct_answer:
                if key in options:
                    correct_texts.append(f"{key}: {options[key]}")
                else:
                    correct_texts.append(str(key))
            return ", ".join(correct_texts)

        elif options and isinstance(correct_answer, str) and correct_answer in options:
            # Single choice - show option text
            return f"{correct_answer}: {options[correct_answer]}"

        else:
            # Direct answer (image identification, answer in word, etc.)
            return str(correct_answer)

